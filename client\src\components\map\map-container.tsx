import { useEffect, useRef, useState } from "react";
import { Plus, X, ZoomIn, ZoomOut, Navigation, MapPin } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { initializeMap, addReportToMap, centerMapOnLocation } from "@/lib/map-utils";
import { useQuery } from "@tanstack/react-query";
import { AccidentReport } from "@shared/schema";
import type { Map } from "ol";

interface MapContainerProps {
  onMapClick: (coordinates: [number, number]) => void;
  isAddingReport: boolean;
  onStartAddingReport: () => void;
  onCancelAddingReport: () => void;
}

export default function MapContainer({
  onMapClick,
  isAddingReport,
  onStartAddingReport,
  onCancelAddingReport,
}: MapContainerProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<Map | null>(null);

  const { data: reports } = useQuery<AccidentReport[]>({
    queryKey: ["/api/reports/recent"],
    refetchInterval: 30000,
  });

  // Initialize map
  useEffect(() => {
    if (!mapRef.current || mapInstanceRef.current) return;

    const map = initializeMap(mapRef.current, onMapClick);
    mapInstanceRef.current = map;

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.setTarget(undefined);
        mapInstanceRef.current = null;
      }
    };
  }, [onMapClick]);

  // Update map with reports
  useEffect(() => {
    if (mapInstanceRef.current && reports) {
      reports.forEach(report => {
        addReportToMap(mapInstanceRef.current!, report);
      });
    }
  }, [reports]);

  const handleZoomIn = () => {
    if (mapInstanceRef.current) {
      const view = mapInstanceRef.current.getView();
      const zoom = view.getZoom();
      if (zoom !== undefined) {
        view.setZoom(zoom + 1);
      }
    }
  };

  const handleZoomOut = () => {
    if (mapInstanceRef.current) {
      const view = mapInstanceRef.current.getView();
      const zoom = view.getZoom();
      if (zoom !== undefined) {
        view.setZoom(zoom - 1);
      }
    }
  };

  const handleCenterMap = () => {
    if (mapInstanceRef.current) {
      const view = mapInstanceRef.current.getView();
      view.setCenter([0, 0]);
      view.setZoom(2);
    }
  };

  const handleGetCurrentLocation = () => {
    if (navigator.geolocation && mapInstanceRef.current) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          centerMapOnLocation(mapInstanceRef.current!, [longitude, latitude]);
        },
        (error) => {
          console.error('Error getting location:', error);
        }
      );
    }
  };

  return (
    <div className="flex-1 relative">
      {/* Map container */}
      <div ref={mapRef} className="w-full h-full bg-neutral-100" />
      
      {/* Map controls */}
      <div className="absolute top-4 right-4 flex flex-col space-y-2 z-30">
        <Button
          variant="outline"
          size="icon"
          onClick={handleZoomIn}
          className="w-10 h-10 bg-white shadow-md hover:bg-neutral-50"
          title="Zoom In"
        >
          <ZoomIn className="w-4 h-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={handleZoomOut}
          className="w-10 h-10 bg-white shadow-md hover:bg-neutral-50"
          title="Zoom Out"
        >
          <ZoomOut className="w-4 h-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={handleCenterMap}
          className="w-10 h-10 bg-white shadow-md hover:bg-neutral-50"
          title="Center Map"
        >
          <Navigation className="w-4 h-4" />
        </Button>
      </div>

      {/* Location button */}
      <Button
        variant="outline"
        size="icon"
        onClick={handleGetCurrentLocation}
        className="absolute bottom-20 right-4 w-12 h-12 bg-white rounded-full shadow-lg hover:bg-neutral-50 z-30"
        title="My Location"
      >
        <MapPin className="w-4 h-4" />
      </Button>

      {/* Add report button */}
      <Button
        onClick={isAddingReport ? onCancelAddingReport : onStartAddingReport}
        className={cn(
          "fixed bottom-6 right-6 w-14 h-14 rounded-full shadow-lg z-40 transition-all duration-200 hover:scale-105",
          isAddingReport 
            ? "bg-red-500 hover:bg-red-600" 
            : "bg-primary hover:bg-blue-700"
        )}
        title={isAddingReport ? "Cancel" : "Add Accident Report"}
      >
        {isAddingReport ? (
          <X className="w-5 h-5" />
        ) : (
          <Plus className="w-5 h-5" />
        )}
      </Button>

      {/* Map instructions */}
      {isAddingReport && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-lg p-4 z-30 animate-fade-in">
          <div className="text-center">
            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center mx-auto mb-2">
              <MapPin className="w-4 h-4 text-white" />
            </div>
            <p className="text-sm font-medium text-neutral-900 mb-1">Click on the map</p>
            <p className="text-xs text-neutral-600">to mark accident location</p>
          </div>
        </div>
      )}
    </div>
  );
}
