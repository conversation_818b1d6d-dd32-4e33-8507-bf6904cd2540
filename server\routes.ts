import type { Express } from "express";
import express from "express";
import { createServer, type Server } from "http";
import multer from "multer";
import path from "path";
import fs from "fs";
import { storage } from "./storage";
import { insertAccidentReportSchema } from "@shared/schema";
import { z } from "zod";

// Configure multer for file uploads
const uploadDir = path.join(process.cwd(), "uploads");
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

const upload = multer({
  dest: uploadDir,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 5, // Max 5 files
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
});

export async function registerRoutes(app: Express): Promise<Server> {
  // Serve uploaded files
  app.use('/uploads', express.static(uploadDir));

  // Get recent accident reports (last 7 days)
  app.get('/api/reports/recent', async (req, res) => {
    try {
      const daysBack = parseInt(req.query.days as string) || 7;
      const reports = await storage.getRecentAccidentReports(daysBack);
      res.json(reports);
    } catch (error) {
      console.error('Error fetching recent reports:', error);
      res.status(500).json({ message: 'Failed to fetch recent reports' });
    }
  });

  // Get all accident reports
  app.get('/api/reports', async (req, res) => {
    try {
      const reports = await storage.getAllAccidentReports();
      res.json(reports);
    } catch (error) {
      console.error('Error fetching reports:', error);
      res.status(500).json({ message: 'Failed to fetch reports' });
    }
  });

  // Get specific accident report
  app.get('/api/reports/:id', async (req, res) => {
    try {
      const report = await storage.getAccidentReport(req.params.id);
      if (!report) {
        return res.status(404).json({ message: 'Report not found' });
      }
      res.json(report);
    } catch (error) {
      console.error('Error fetching report:', error);
      res.status(500).json({ message: 'Failed to fetch report' });
    }
  });

  // Create new accident report with image uploads
  app.post('/api/reports', upload.array('images', 5), async (req, res) => {
    try {
      // Parse and validate report data
      const reportData = {
        ...req.body,
        latitude: parseFloat(req.body.latitude),
        longitude: parseFloat(req.body.longitude),
      };

      const validatedData = insertAccidentReportSchema.parse(reportData);

      // Handle uploaded images
      const imageUrls: string[] = [];
      if (req.files && Array.isArray(req.files)) {
        for (const file of req.files) {
          const filename = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}${path.extname(file.originalname)}`;
          const newPath = path.join(uploadDir, filename);
          fs.renameSync(file.path, newPath);
          imageUrls.push(`/uploads/${filename}`);
        }
      }

      // Create report with image URLs
      const report = await storage.createAccidentReport({
        ...validatedData,
        imageUrls,
      });

      res.status(201).json(report);
    } catch (error) {
      console.error('Error creating report:', error);
      
      // Clean up uploaded files on error
      if (req.files && Array.isArray(req.files)) {
        req.files.forEach(file => {
          if (fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
          }
        });
      }

      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: 'Validation error', 
          errors: error.errors 
        });
      }

      res.status(500).json({ message: 'Failed to create report' });
    }
  });

  // Delete accident report
  app.delete('/api/reports/:id', async (req, res) => {
    try {
      const report = await storage.getAccidentReport(req.params.id);
      if (!report) {
        return res.status(404).json({ message: 'Report not found' });
      }

      // Delete associated image files
      if (report.imageUrls && report.imageUrls.length > 0) {
        report.imageUrls.forEach(url => {
          const filename = path.basename(url);
          const filePath = path.join(uploadDir, filename);
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
          }
        });
      }

      const deleted = await storage.deleteAccidentReport(req.params.id);
      if (deleted) {
        res.json({ message: 'Report deleted successfully' });
      } else {
        res.status(500).json({ message: 'Failed to delete report' });
      }
    } catch (error) {
      console.error('Error deleting report:', error);
      res.status(500).json({ message: 'Failed to delete report' });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
