<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accident Reporting GIS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://unpkg.com/ol@8.2.0/ol.css" type="text/css">
    <script src="https://unpkg.com/ol@8.2.0/dist/ol.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        primary: '#1976D2',
                        secondary: '#F57C00',
                        success: '#388E3C',
                        error: '#D32F2F',
                        warning: '#F57C00',
                        neutral: {
                            50: '#FAFAFA',
                            100: '#F5F5F5',
                            200: '#E0E0E0',
                            300: '#BDBDBD',
                            500: '#757575',
                            800: '#424242',
                            900: '#212121'
                        }
                    }
                }
            }
        };
    </script>
</head>
<body class="font-inter bg-neutral-50 overflow-hidden">
    <!-- @COMPONENT: AppHeader -->
    <header class="bg-white shadow-sm border-b border-neutral-200 relative z-50">
        <div class="px-4 py-3 flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <i class="fas fa-map-marked-alt text-primary text-xl"></i>
                <h1 class="text-lg font-semibold text-neutral-900">Accident Reporter</h1>
            </div>
            <div class="flex items-center space-x-2">
                <button class="lg:hidden p-2 text-neutral-600 hover:text-primary transition-colors" data-event="click:toggleSidebar">
                    <i class="fas fa-bars text-lg"></i>
                </button>
                <button class="hidden lg:flex items-center space-x-2 px-3 py-1.5 text-sm text-neutral-600 hover:bg-neutral-100 rounded-lg transition-colors" data-event="click:showFilters">
                    <i class="fas fa-filter text-xs"></i>
                    <span>Last 7 days</span>
                </button>
            </div>
        </div>
    </header>
    <!-- @END_COMPONENT: AppHeader -->

    <div class="flex h-screen pt-16">
        <!-- @COMPONENT: ReportsSidebar -->
        <div id="sidebar" class="w-80 bg-white border-r border-neutral-200 flex-shrink-0 transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out absolute lg:relative z-40 h-full">
            <div class="h-full flex flex-col">
                <div class="p-4 border-b border-neutral-200">
                    <div class="flex items-center justify-between mb-3">
                        <h2 class="text-lg font-semibold text-neutral-900">Recent Reports</h2>
                        <button class="lg:hidden text-neutral-500 hover:text-neutral-700" data-event="click:closeSidebar">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-neutral-600">
                        <i class="fas fa-clock text-xs"></i>
                        <span data-bind="reportsCount">12 reports in last 7 days</span>
                    </div>
                </div>
                
                <div class="flex-1 overflow-y-auto">
                    <!-- @MAP: recentReports.map(report => ( -->
                    <div class="border-b border-neutral-100 p-4 hover:bg-neutral-50 cursor-pointer transition-colors" data-event="click:zoomToReport" data-mock="true">
                        <div class="flex items-start space-x-3">
                            <div class="w-2 h-2 bg-error rounded-full mt-2 flex-shrink-0"></div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm font-medium text-neutral-900" data-bind="report.type">Vehicle Collision</span>
                                    <span class="text-xs text-neutral-500" data-bind="report.timeAgo">2h ago</span>
                                </div>
                                <p class="text-sm text-neutral-600 line-clamp-2 mb-2" data-bind="report.description">
                                    Two-car accident at Main St intersection. Minor injuries reported. Emergency services responded.
                                </p>
                                <div class="flex items-center justify-between">
                                    <span class="text-xs text-neutral-500" data-bind="report.location">Main St & 5th Ave</span>
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-camera text-xs text-neutral-400"></i>
                                        <span class="text-xs text-neutral-500" data-bind="report.imageCount">3</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="border-b border-neutral-100 p-4 hover:bg-neutral-50 cursor-pointer transition-colors" data-event="click:zoomToReport" data-mock="true">
                        <div class="flex items-start space-x-3">
                            <div class="w-2 h-2 bg-warning rounded-full mt-2 flex-shrink-0"></div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm font-medium text-neutral-900">Road Hazard</span>
                                    <span class="text-xs text-neutral-500">4h ago</span>
                                </div>
                                <p class="text-sm text-neutral-600 line-clamp-2 mb-2">
                                    Large pothole causing vehicle damage. Multiple reports received from motorists.
                                </p>
                                <div class="flex items-center justify-between">
                                    <span class="text-xs text-neutral-500">Highway 101 Mile 23</span>
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-camera text-xs text-neutral-400"></i>
                                        <span class="text-xs text-neutral-500">1</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="border-b border-neutral-100 p-4 hover:bg-neutral-50 cursor-pointer transition-colors" data-event="click:zoomToReport" data-mock="true">
                        <div class="flex items-start space-x-3">
                            <div class="w-2 h-2 bg-error rounded-full mt-2 flex-shrink-0"></div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm font-medium text-neutral-900">Pedestrian Accident</span>
                                    <span class="text-xs text-neutral-500">1d ago</span>
                                </div>
                                <p class="text-sm text-neutral-600 line-clamp-2 mb-2">
                                    Pedestrian struck at crosswalk. Ambulance dispatched, victim transported to hospital.
                                </p>
                                <div class="flex items-center justify-between">
                                    <span class="text-xs text-neutral-500">Oak Ave & 2nd St</span>
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-camera text-xs text-neutral-400"></i>
                                        <span class="text-xs text-neutral-500">2</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="border-b border-neutral-100 p-4 hover:bg-neutral-50 cursor-pointer transition-colors" data-event="click:zoomToReport" data-mock="true">
                        <div class="flex items-start space-x-3">
                            <div class="w-2 h-2 bg-secondary rounded-full mt-2 flex-shrink-0"></div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm font-medium text-neutral-900">Traffic Signal Issue</span>
                                    <span class="text-xs text-neutral-500">2d ago</span>
                                </div>
                                <p class="text-sm text-neutral-600 line-clamp-2 mb-2">
                                    Traffic light stuck on red causing major backups during rush hour.
                                </p>
                                <div class="flex items-center justify-between">
                                    <span class="text-xs text-neutral-500">Central Ave & Pine St</span>
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-camera text-xs text-neutral-400"></i>
                                        <span class="text-xs text-neutral-500">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- @END_MAP )) -->
                </div>
            </div>
        </div>
        <!-- @END_COMPONENT: ReportsSidebar -->

        <!-- @COMPONENT: MapContainer -->
        <div class="flex-1 relative">
            <!-- @FUNCTIONALITY: This map should implement OpenLayers with tile layer, vector layer for points, and click-to-add functionality -->
            <div id="map" class="w-full h-full bg-neutral-100" data-implementation="Initialize OpenLayers map with OSM tiles and vector layer for accident points"></div>
            
            <!-- Map Controls -->
            <div class="absolute top-4 right-4 flex flex-col space-y-2 z-30">
                <button class="w-10 h-10 bg-white rounded-lg shadow-md flex items-center justify-center text-neutral-600 hover:text-primary hover:bg-neutral-50 transition-colors" data-event="click:zoomIn" title="Zoom In">
                    <i class="fas fa-plus text-sm"></i>
                </button>
                <button class="w-10 h-10 bg-white rounded-lg shadow-md flex items-center justify-center text-neutral-600 hover:text-primary hover:bg-neutral-50 transition-colors" data-event="click:zoomOut" title="Zoom Out">
                    <i class="fas fa-minus text-sm"></i>
                </button>
                <button class="w-10 h-10 bg-white rounded-lg shadow-md flex items-center justify-center text-neutral-600 hover:text-primary hover:bg-neutral-50 transition-colors" data-event="click:centerMap" title="Center Map">
                    <i class="fas fa-crosshairs text-sm"></i>
                </button>
            </div>

            <!-- Location Button -->
            <button class="absolute bottom-20 right-4 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center text-neutral-600 hover:text-primary hover:bg-neutral-50 transition-colors z-30" data-event="click:getCurrentLocation" title="My Location">
                <i class="fas fa-location-arrow text-sm"></i>
            </button>

            <!-- Add Report FAB -->
            <button id="addReportBtn" class="fixed bottom-6 right-6 w-14 h-14 bg-primary text-white rounded-full shadow-lg hover:bg-blue-700 transition-all duration-200 flex items-center justify-center z-40 hover:scale-105" data-event="click:startAddingReport" title="Add Accident Report">
                <i class="fas fa-plus text-lg"></i>
            </button>

            <!-- Map Instructions (shown when adding report) -->
            <div id="mapInstructions" class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-lg p-4 z-30 hidden">
                <div class="text-center">
                    <i class="fas fa-mouse-pointer text-primary text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-neutral-900 mb-1">Click on the map</p>
                    <p class="text-xs text-neutral-600">to mark accident location</p>
                </div>
            </div>
        </div>
        <!-- @END_COMPONENT: MapContainer -->
    </div>

    <!-- @COMPONENT: ReportModal -->
    <div id="reportModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-xl shadow-2xl w-full max-w-md mx-auto max-h-[90vh] overflow-hidden">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-semibold text-neutral-900">Report Accident</h3>
                        <button class="text-neutral-400 hover:text-neutral-600 transition-colors" data-event="click:closeModal">
                            <i class="fas fa-times text-lg"></i>
                        </button>
                    </div>

                    <form id="reportForm" class="space-y-4" data-event="submit:submitReport">
                        <!-- Location Display -->
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 mb-2">Location</label>
                            <div class="flex items-center space-x-2 p-3 bg-neutral-50 rounded-lg">
                                <i class="fas fa-map-marker-alt text-primary text-sm"></i>
                                <span class="text-sm text-neutral-600" data-bind="selectedLocation.address" data-mock="true">123 Main Street, Downtown</span>
                            </div>
                        </div>

                        <!-- Accident Type -->
                        <div>
                            <label for="accidentType" class="block text-sm font-medium text-neutral-700 mb-2">Accident Type *</label>
                            <select id="accidentType" class="w-full p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" required>
                                <option value="">Select accident type</option>
                                <option value="vehicle-collision">Vehicle Collision</option>
                                <option value="pedestrian-accident">Pedestrian Accident</option>
                                <option value="bicycle-accident">Bicycle Accident</option>
                                <option value="road-hazard">Road Hazard</option>
                                <option value="traffic-signal">Traffic Signal Issue</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <!-- Description -->
                        <div>
                            <label for="description" class="block text-sm font-medium text-neutral-700 mb-2">Description *</label>
                            <textarea id="description" rows="3" class="w-full p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent resize-none" placeholder="Describe what happened..." required></textarea>
                            <p class="text-xs text-neutral-500 mt-1">Minimum 10 characters</p>
                        </div>

                        <!-- Severity -->
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 mb-2">Severity</label>
                            <div class="flex space-x-2">
                                <label class="flex items-center space-x-2 cursor-pointer">
                                    <input type="radio" name="severity" value="minor" class="text-primary focus:ring-primary" checked>
                                    <span class="text-sm text-neutral-700">Minor</span>
                                </label>
                                <label class="flex items-center space-x-2 cursor-pointer">
                                    <input type="radio" name="severity" value="major" class="text-primary focus:ring-primary">
                                    <span class="text-sm text-neutral-700">Major</span>
                                </label>
                                <label class="flex items-center space-x-2 cursor-pointer">
                                    <input type="radio" name="severity" value="critical" class="text-primary focus:ring-primary">
                                    <span class="text-sm text-neutral-700">Critical</span>
                                </label>
                            </div>
                        </div>

                        <!-- Image Upload -->
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 mb-2">Photos</label>
                            <div class="border-2 border-dashed border-neutral-300 rounded-lg p-4 text-center hover:border-primary transition-colors cursor-pointer" data-implementation="Implement file drop zone">
                                <input type="file" id="imageUpload" multiple accept="image/*" class="hidden" data-event="change:handleImageUpload">
                                <label for="imageUpload" class="cursor-pointer">
                                    <i class="fas fa-cloud-upload-alt text-2xl text-neutral-400 mb-2"></i>
                                    <p class="text-sm text-neutral-600">Click to upload or drag photos here</p>
                                    <p class="text-xs text-neutral-500 mt-1">Max 5 photos, 10MB each</p>
                                </label>
                            </div>
                            
                            <!-- Preview uploaded images -->
                            <div id="imagePreview" class="mt-3 grid grid-cols-3 gap-2 hidden">
                                <!-- Uploaded images will appear here -->
                            </div>
                        </div>

                        <!-- Contact Info (Optional) -->
                        <div>
                            <label for="contactInfo" class="block text-sm font-medium text-neutral-700 mb-2">Contact Info (Optional)</label>
                            <input type="text" id="contactInfo" class="w-full p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Phone or email (optional)">
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex space-x-3 pt-4">
                            <button type="button" class="flex-1 px-4 py-3 text-neutral-600 bg-neutral-100 rounded-lg hover:bg-neutral-200 transition-colors font-medium" data-event="click:closeModal">
                                Cancel
                            </button>
                            <button type="submit" class="flex-1 px-4 py-3 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                                Submit Report
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- @END_COMPONENT: ReportModal -->

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-white bg-opacity-90 z-50 hidden flex items-center justify-center">
        <div class="text-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-3"></div>
            <p class="text-sm text-neutral-600">Loading map...</p>
        </div>
    </div>

    <!-- Success Toast -->
    <div id="successToast" class="fixed top-20 right-4 bg-success text-white px-4 py-3 rounded-lg shadow-lg z-50 hidden">
        <div class="flex items-center space-x-2">
            <i class="fas fa-check-circle"></i>
            <span class="text-sm font-medium">Report submitted successfully!</span>
        </div>
    </div>

    <script>
        (function() {
            // TODO: Initialize OpenLayers map
            // TODO: Implement click-to-add-point functionality  
            // TODO: Connect to backend API for data persistence
            // TODO: Implement image upload and file handling
            // TODO: Add form validation and error handling
            // TODO: Implement geolocation services
            // TODO: Add real-time updates for new reports

            let map;
            let isAddingReport = false;
            let selectedCoordinates = null;

            // Initialize the application
            function initApp() {
                initMap();
                bindEventHandlers();
                showLoadingOverlay(false);
            }

            function initMap() {
                // Initialize OpenLayers map - placeholder for actual implementation
                const mapElement = document.getElementById('map');
                console.log('Map would be initialized here with OpenLayers');
                
                // Simulate map loading
                setTimeout(() => {
                    mapElement.style.background = 'linear-gradient(45deg, #e8f4fd 25%, transparent 25%), linear-gradient(-45deg, #e8f4fd 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #e8f4fd 75%), linear-gradient(-45deg, transparent 75%, #e8f4fd 75%)';
                    mapElement.style.backgroundSize = '20px 20px';
                    mapElement.style.backgroundPosition = '0 0, 0 10px, 10px -10px, -10px 0px';
                }, 500);
            }

            function bindEventHandlers() {
                // Mobile sidebar toggle
                document.addEventListener('click', function(e) {
                    const sidebar = document.getElementById('sidebar');
                    
                    if (e.target.closest('[data-event="click:toggleSidebar"]')) {
                        sidebar.classList.toggle('-translate-x-full');
                    }
                    
                    if (e.target.closest('[data-event="click:closeSidebar"]')) {
                        sidebar.classList.add('-translate-x-full');
                    }

                    // Add report functionality
                    if (e.target.closest('[data-event="click:startAddingReport"]')) {
                        startAddingReport();
                    }

                    // Modal controls
                    if (e.target.closest('[data-event="click:closeModal"]')) {
                        closeModal();
                    }
                });

                // Form submission
                document.getElementById('reportForm').addEventListener('submit', function(e) {
                    e.preventDefault();
                    submitReport();
                });

                // Close modal on backdrop click
                document.getElementById('reportModal').addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeModal();
                    }
                });

                // Close sidebar on backdrop click (mobile)
                document.addEventListener('click', function(e) {
                    const sidebar = document.getElementById('sidebar');
                    if (window.innerWidth < 1024 && !sidebar.contains(e.target) && !e.target.closest('[data-event="click:toggleSidebar"]')) {
                        sidebar.classList.add('-translate-x-full');
                    }
                });
            }

            function startAddingReport() {
                isAddingReport = true;
                document.getElementById('mapInstructions').classList.remove('hidden');
                document.getElementById('addReportBtn').innerHTML = '<i class="fas fa-times text-lg"></i>';
                document.getElementById('addReportBtn').setAttribute('data-event', 'click:cancelAddingReport');
                
                // Simulate map click to add point
                setTimeout(() => {
                    if (isAddingReport) {
                        selectedCoordinates = { lat: 37.7749, lng: -122.4194 }; // San Francisco coords
                        showReportModal();
                    }
                }, 2000);
            }

            function cancelAddingReport() {
                isAddingReport = false;
                document.getElementById('mapInstructions').classList.add('hidden');
                document.getElementById('addReportBtn').innerHTML = '<i class="fas fa-plus text-lg"></i>';
                document.getElementById('addReportBtn').setAttribute('data-event', 'click:startAddingReport');
            }

            function showReportModal() {
                document.getElementById('reportModal').classList.remove('hidden');
                document.getElementById('mapInstructions').classList.add('hidden');
                isAddingReport = false;
                document.getElementById('addReportBtn').innerHTML = '<i class="fas fa-plus text-lg"></i>';
                document.getElementById('addReportBtn').setAttribute('data-event', 'click:startAddingReport');
            }

            function closeModal() {
                document.getElementById('reportModal').classList.add('hidden');
                document.getElementById('reportForm').reset();
                selectedCoordinates = null;
            }

            function submitReport() {
                // Basic form validation
                const form = document.getElementById('reportForm');
                const formData = new FormData(form);
                
                if (!formData.get('accidentType') || !formData.get('description') || formData.get('description').length < 10) {
                    alert('Please fill in all required fields with sufficient detail.');
                    return;
                }

                // Show success message
                closeModal();
                showSuccessToast();
                
                // In real implementation, submit to API
                console.log('Report would be submitted to API:', {
                    location: selectedCoordinates,
                    type: formData.get('accidentType'),
                    description: formData.get('description'),
                    severity: formData.get('severity'),
                    contact: formData.get('contactInfo')
                });
            }

            function showSuccessToast() {
                const toast = document.getElementById('successToast');
                toast.classList.remove('hidden');
                setTimeout(() => {
                    toast.classList.add('hidden');
                }, 3000);
            }

            function showLoadingOverlay(show) {
                const overlay = document.getElementById('loadingOverlay');
                if (show) {
                    overlay.classList.remove('hidden');
                } else {
                    overlay.classList.add('hidden');
                }
            }

            // Initialize app when DOM is loaded
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initApp);
            } else {
                initApp();
            }
        })();
    </script>
</body>
</html>