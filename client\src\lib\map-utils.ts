import { Map, View } from 'ol';
import TileLayer from 'ol/layer/Tile';
import OSM from 'ol/source/OSM';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import { Feature } from 'ol';
import { Point } from 'ol/geom';
import { fromLonLat, toLonLat } from 'ol/proj';
import { Style, Circle, Fill, Stroke } from 'ol/style';
import { Overlay } from 'ol';
import type { FeatureLike } from 'ol/Feature';
import { AccidentReport } from '@shared/schema';

let vectorSource: VectorSource;
let vectorLayer: VectorLayer<VectorSource>;

export function initializeMap(target: HTMLElement, onMapClick: (coordinates: [number, number]) => void): Map {
  // Create vector source and layer for accident points
  vectorSource = new VectorSource();
  vectorLayer = new VectorLayer({
    source: vectorSource,
    style: createPointStyle,
  });

  const map = new Map({
    target,
    layers: [
      new TileLayer({
        source: new OSM(),
      }),
      vectorLayer,
    ],
    view: new View({
      center: fromLonLat([-122.4194, 37.7749]), // San Francisco
      zoom: 10,
    }),
  });

  // Add click handler
  map.on('singleclick', (event) => {
    const coordinates = toLonLat(event.coordinate);
    onMapClick([coordinates[0], coordinates[1]]);
  });

  return map;
}

function createPointStyle(feature: FeatureLike): Style {
  const report = feature.get('report') as AccidentReport;
  let color = '#1976D2'; // Default blue
  
  if (report) {
    switch (report.severity) {
      case 'critical':
        color = '#D32F2F'; // Red
        break;
      case 'major':
        color = '#F57C00'; // Orange
        break;
      case 'minor':
        color = '#1976D2'; // Blue
        break;
    }
  }

  return new Style({
    image: new Circle({
      radius: 8,
      fill: new Fill({ color }),
      stroke: new Stroke({
        color: '#ffffff',
        width: 2,
      }),
    }),
  });
}

export function addReportToMap(map: Map, report: AccidentReport): void {
  if (!vectorSource) return;

  // Check if feature already exists
  const existingFeature = vectorSource.getFeatures().find(
    feature => feature.get('reportId') === report.id
  );
  
  if (existingFeature) return;

  const feature = new Feature({
    geometry: new Point(fromLonLat([report.longitude, report.latitude])),
    report,
    reportId: report.id,
  });

  vectorSource.addFeature(feature);

  // Add popup on click
  map.on('singleclick', (event) => {
    const features = map.getFeaturesAtPixel(event.pixel);
    if (features.length > 0) {
      const clickedFeature = features[0];
      const clickedReport = clickedFeature.get('report');
      
      if (clickedReport) {
        showReportPopup(map, event.coordinate, clickedReport);
      }
    }
  });
}

function showReportPopup(map: Map, coordinate: number[], report: AccidentReport): void {
  // Remove existing popups
  const existingOverlays = map.getOverlays().getArray().slice();
  existingOverlays.forEach(overlay => map.removeOverlay(overlay));

  // Create popup element
  const popupElement = document.createElement('div');
  popupElement.className = 'map-popup';
  
  const timeAgo = formatTimeAgo(new Date(report.createdAt));
  const formattedType = formatReportType(report.type);
  
  popupElement.innerHTML = `
    <div class="space-y-2">
      <div class="flex items-center justify-between">
        <h4 class="font-medium text-sm text-neutral-900">${formattedType}</h4>
        <span class="text-xs text-neutral-500">${timeAgo}</span>
      </div>
      <p class="text-xs text-neutral-600 line-clamp-2">${report.description}</p>
      <div class="flex items-center justify-between">
        <span class="text-xs font-medium text-${getSeverityColor(report.severity)}">${report.severity.toUpperCase()}</span>
        ${report.imageUrls && report.imageUrls.length > 0 ? 
          `<span class="text-xs text-neutral-500">${report.imageUrls.length} photo${report.imageUrls.length > 1 ? 's' : ''}</span>` : 
          ''
        }
      </div>
    </div>
  `;

  // Create overlay
  const popup = new Overlay({
    element: popupElement,
    positioning: 'bottom-center',
    stopEvent: false,
    offset: [0, -20],
  });

  popup.setPosition(coordinate);
  map.addOverlay(popup);

  // Remove popup when clicking elsewhere
  const removePopup = () => {
    map.removeOverlay(popup);
    map.un('singleclick', removePopup);
  };

  setTimeout(() => {
    map.on('singleclick', removePopup);
  }, 100);
}

function formatTimeAgo(date: Date): string {
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
  
  if (diffInHours < 1) {
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    return `${diffInMinutes}m ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  } else {
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  }
}

function formatReportType(type: string): string {
  return type.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');
}

function getSeverityColor(severity: string): string {
  switch (severity) {
    case 'critical': return 'red-600';
    case 'major': return 'orange-600';
    case 'minor': return 'blue-600';
    default: return 'neutral-600';
  }
}

export function centerMapOnLocation(map: Map, coordinates: [number, number]): void {
  const view = map.getView();
  view.setCenter(fromLonLat(coordinates));
  view.setZoom(15);
}
