import { Map<PERSON><PERSON>, <PERSON>u, Filter } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface AppHeaderProps {
  onToggleSidebar: () => void;
  isMobile: boolean;
}

export default function AppHeader({ onToggleSidebar, isMobile }: AppHeaderProps) {
  return (
    <header className="bg-white shadow-sm border-b border-neutral-200 fixed top-0 left-0 right-0 z-50">
      <div className="px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <MapPin className="text-primary text-xl w-6 h-6" />
          <h1 className="text-lg font-semibold text-neutral-900">Accident Reporter</h1>
        </div>
        
        <div className="flex items-center space-x-2">
          {isMobile ? (
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleSidebar}
              className="p-2 text-neutral-600 hover:text-primary"
            >
              <Menu className="w-5 h-5" />
            </Button>
          ) : (
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center space-x-2 px-3 py-1.5 text-sm text-neutral-600 hover:bg-neutral-100"
            >
              <Filter className="w-3 h-3" />
              <span>Last 7 days</span>
            </Button>
          )}
        </div>
      </div>
    </header>
  );
}
