# Accident Reporter

## Overview

A web application for reporting and visualizing traffic accidents and road incidents. Users can submit accident reports with location data, photos, and detailed descriptions, while viewing recent incidents on an interactive map. The application serves as a community-driven platform for tracking road safety issues and providing real-time incident awareness.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React with TypeScript using Vite as the build tool
- **UI Components**: shadcn/ui component library built on Radix UI primitives
- **Styling**: Tailwind CSS with custom design tokens and CSS variables
- **State Management**: TanStack Query (React Query) for server state management
- **Routing**: Wouter for lightweight client-side routing
- **Map Integration**: OpenLayers for interactive mapping functionality with OpenStreetMap tiles

### Backend Architecture
- **Runtime**: Node.js with Express.js web framework
- **Language**: TypeScript with ES modules
- **Database**: PostgreSQL with Drizzle ORM for type-safe database operations
- **File Uploads**: Multer middleware for handling multipart form data and image uploads
- **Storage Strategy**: In-memory storage implementation with interface for easy swapping to persistent storage

### Data Storage Solutions
- **Primary Database**: SQLite local database with spatial indexing for GIS operations
- **Database Location**: `./data/accident_reports.db` (persistent local file storage)
- **Spatial Features**: Haversine distance calculations and location-based queries
- **File Storage**: Local filesystem storage for uploaded images with configurable upload directory
- **Performance**: WAL mode enabled, spatial and temporal indexes for optimal query performance
- **Backup Strategy**: Local database file can be easily backed up or migrated

### API Design
- **Architecture**: RESTful API endpoints under `/api` prefix
- **Data Validation**: Zod schemas for request/response validation and type safety
- **Error Handling**: Centralized error middleware with proper HTTP status codes
- **File Handling**: Multipart form data support for image uploads with size and type restrictions

### Key Features
- **Interactive Map**: Click-to-add incident reporting with real-time visualization
- **Incident Management**: CRUD operations for accident reports with filtering and search
- **Media Support**: Image upload and preview functionality for incident documentation
- **Responsive Design**: Mobile-first responsive layout with adaptive sidebar navigation
- **Real-time Updates**: Automatic data refreshing for live incident tracking

## External Dependencies

### Database Services
- **Neon**: Serverless PostgreSQL database hosting
- **Drizzle Kit**: Database migration and schema management tools

### Map Services
- **OpenLayers**: Interactive mapping library for web applications
- **OpenStreetMap**: Tile service for map visualization

### UI Framework
- **Radix UI**: Headless component primitives for accessibility and interaction
- **Tailwind CSS**: Utility-first CSS framework for styling
- **Lucide React**: Icon library for consistent iconography

### Development Tools
- **Vite**: Frontend build tool and development server
- **TypeScript**: Static type checking for JavaScript
- **ESBuild**: Fast JavaScript bundler for production builds
- **Replit Integration**: Development environment plugins and runtime error handling

### File Processing
- **Multer**: Express middleware for handling file uploads
- **Sharp**: High-performance image processing (implied by file handling patterns)