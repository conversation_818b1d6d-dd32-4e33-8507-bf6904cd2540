import { useState } from "react";
import AppHeader from "@/components/layout/app-header";
import ReportsSidebar from "@/components/sidebar/reports-sidebar";
import MapContainer from "@/components/map/map-container";
import ReportModal from "@/components/map/report-modal";
import { useIsMobile } from "@/hooks/use-mobile";

export default function MapPage() {
  const [isSidebarOpen, setSidebarOpen] = useState(false);
  const [isReportModalOpen, setReportModalOpen] = useState(false);
  const [selectedCoordinates, setSelectedCoordinates] = useState<[number, number] | null>(null);
  const [isAddingReport, setIsAddingReport] = useState(false);
  const isMobile = useIsMobile();

  const handleStartAddingReport = () => {
    setIsAddingReport(true);
    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  const handleMapClick = (coordinates: [number, number]) => {
    if (isAddingReport) {
      setSelectedCoordinates(coordinates);
      setReportModalOpen(true);
      setIsAddingReport(false);
    }
  };

  const handleCancelAddingReport = () => {
    setIsAddingReport(false);
    setSelectedCoordinates(null);
  };

  const handleReportSubmitted = () => {
    setReportModalOpen(false);
    setSelectedCoordinates(null);
    setIsAddingReport(false);
  };

  return (
    <div className="h-screen flex flex-col bg-neutral-50 overflow-hidden">
      <AppHeader 
        onToggleSidebar={() => setSidebarOpen(!isSidebarOpen)}
        isMobile={isMobile}
      />
      
      <div className="flex flex-1 pt-16">
        <ReportsSidebar 
          isOpen={isSidebarOpen}
          onClose={() => setSidebarOpen(false)}
          isMobile={isMobile}
        />
        
        <MapContainer
          onMapClick={handleMapClick}
          isAddingReport={isAddingReport}
          onStartAddingReport={handleStartAddingReport}
          onCancelAddingReport={handleCancelAddingReport}
        />
      </div>

      <ReportModal
        isOpen={isReportModalOpen}
        onClose={() => setReportModalOpen(false)}
        coordinates={selectedCoordinates}
        onReportSubmitted={handleReportSubmitted}
      />
    </div>
  );
}
