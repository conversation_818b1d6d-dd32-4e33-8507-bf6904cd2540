import { useQuery } from "@tanstack/react-query";
import { Clock, Camera, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { AccidentReport } from "@shared/schema";

interface ReportsSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  isMobile: boolean;
}

function formatTimeAgo(date: Date): string {
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
  
  if (diffInHours < 1) {
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    return `${diffInMinutes}m ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  } else {
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  }
}

function getReportTypeColor(type: string, severity: string): string {
  if (severity === "critical") return "bg-red-500";
  if (severity === "major") return "bg-orange-500";
  
  switch (type) {
    case "vehicle-collision":
    case "pedestrian-accident":
      return "bg-red-500";
    case "road-hazard":
    case "traffic-signal":
      return "bg-amber-500";
    default:
      return "bg-blue-500";
  }
}

function formatReportType(type: string): string {
  return type.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');
}

function ReportItem({ report }: { report: AccidentReport }) {
  const typeColor = getReportTypeColor(report.type, report.severity);
  const timeAgo = formatTimeAgo(new Date(report.createdAt));
  const formattedType = formatReportType(report.type);
  const imageCount = report.imageUrls?.length || 0;

  return (
    <div className="border-b border-neutral-100 p-4 hover:bg-neutral-50 cursor-pointer transition-colors">
      <div className="flex items-start space-x-3">
        <div className={cn("w-2 h-2 rounded-full mt-2 flex-shrink-0", typeColor)} />
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm font-medium text-neutral-900">{formattedType}</span>
            <span className="text-xs text-neutral-500">{timeAgo}</span>
          </div>
          <p className="text-sm text-neutral-600 line-clamp-2 mb-2">
            {report.description}
          </p>
          <div className="flex items-center justify-between">
            <span className="text-xs text-neutral-500">
              {report.address || `${report.latitude.toFixed(4)}, ${report.longitude.toFixed(4)}`}
            </span>
            {imageCount > 0 && (
              <div className="flex items-center space-x-1">
                <Camera className="w-3 h-3 text-neutral-400" />
                <span className="text-xs text-neutral-500">{imageCount}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ReportsSidebar({ isOpen, onClose, isMobile }: ReportsSidebarProps) {
  const { data: reports, isLoading, error } = useQuery<AccidentReport[]>({
    queryKey: ["/api/reports/recent"],
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  return (
    <>
      {/* Backdrop for mobile */}
      {isMobile && isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-30"
          onClick={onClose}
        />
      )}
      
      <div className={cn(
        "w-80 bg-white border-r border-neutral-200 flex-shrink-0 transition-transform duration-300 ease-in-out z-40 h-full",
        isMobile ? "fixed" : "relative",
        isMobile && !isOpen ? "-translate-x-full" : "translate-x-0"
      )}>
        <div className="h-full flex flex-col">
          <div className="p-4 border-b border-neutral-200">
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-lg font-semibold text-neutral-900">Recent Reports</h2>
              {isMobile && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  className="text-neutral-500 hover:text-neutral-700 p-1"
                >
                  <X className="w-4 h-4" />
                </Button>
              )}
            </div>
            <div className="flex items-center space-x-2 text-sm text-neutral-600">
              <Clock className="w-3 h-3" />
              <span>
                {isLoading ? (
                  <Skeleton className="h-4 w-32" />
                ) : error ? (
                  "Error loading reports"
                ) : (
                  `${reports?.length || 0} reports in last 7 days`
                )}
              </span>
            </div>
          </div>
          
          <div className="flex-1 overflow-y-auto scrollbar-thin">
            {isLoading ? (
              <div className="space-y-4 p-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-12" />
                    </div>
                    <Skeleton className="h-10 w-full" />
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-3 w-32" />
                      <Skeleton className="h-3 w-8" />
                    </div>
                  </div>
                ))}
              </div>
            ) : error ? (
              <div className="p-4 text-center text-neutral-500">
                <p>Failed to load reports</p>
                <p className="text-xs mt-1">Please try again later</p>
              </div>
            ) : reports && reports.length > 0 ? (
              reports.map((report) => (
                <ReportItem key={report.id} report={report} />
              ))
            ) : (
              <div className="p-4 text-center text-neutral-500">
                <p>No recent reports</p>
                <p className="text-xs mt-1">Reports from the last 7 days will appear here</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
