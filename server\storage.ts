import { type User, type InsertUser, type AccidentReport, type InsertAccidentReport } from "@shared/schema";
import { randomUUID } from "crypto";

export interface IStorage {
  // User methods
  getUser(id: string): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Accident report methods
  getAccidentReport(id: string): Promise<AccidentReport | undefined>;
  getAllAccidentReports(): Promise<AccidentReport[]>;
  getRecentAccidentReports(daysBack: number): Promise<AccidentReport[]>;
  createAccidentReport(report: InsertAccidentReport): Promise<AccidentReport>;
  updateAccidentReport(id: string, report: Partial<AccidentReport>): Promise<AccidentReport | undefined>;
  deleteAccidentReport(id: string): Promise<boolean>;
}

export class MemStorage implements IStorage {
  private users: Map<string, User>;
  private accidentReports: Map<string, AccidentReport>;

  constructor() {
    this.users = new Map();
    this.accidentReports = new Map();
  }

  // User methods
  async getUser(id: string): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = randomUUID();
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  // Accident report methods
  async getAccidentReport(id: string): Promise<AccidentReport | undefined> {
    return this.accidentReports.get(id);
  }

  async getAllAccidentReports(): Promise<AccidentReport[]> {
    return Array.from(this.accidentReports.values());
  }

  async getRecentAccidentReports(daysBack: number = 7): Promise<AccidentReport[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysBack);
    
    return Array.from(this.accidentReports.values())
      .filter(report => new Date(report.createdAt) >= cutoffDate)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  async createAccidentReport(insertReport: InsertAccidentReport): Promise<AccidentReport> {
    const id = randomUUID();
    const report: AccidentReport = {
      ...insertReport,
      id,
      address: insertReport.address || null,
      contactInfo: insertReport.contactInfo || null,
      imageUrls: insertReport.imageUrls || [],
      createdAt: new Date(),
    };
    this.accidentReports.set(id, report);
    return report;
  }

  async updateAccidentReport(id: string, updateData: Partial<AccidentReport>): Promise<AccidentReport | undefined> {
    const existingReport = this.accidentReports.get(id);
    if (!existingReport) {
      return undefined;
    }
    
    const updatedReport = { ...existingReport, ...updateData };
    this.accidentReports.set(id, updatedReport);
    return updatedReport;
  }

  async deleteAccidentReport(id: string): Promise<boolean> {
    return this.accidentReports.delete(id);
  }
}

import { SQLiteStorage } from "./sqlite-storage";

export const storage = new SQLiteStorage();
