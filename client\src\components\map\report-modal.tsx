import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { X, Upload, Camera } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

interface ReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  coordinates: [number, number] | null;
  onReportSubmitted: () => void;
}

interface FormData {
  type: string;
  description: string;
  severity: string;
  contactInfo: string;
  images: File[];
}

export default function ReportModal({
  isOpen,
  onClose,
  coordinates,
  onReportSubmitted,
}: ReportModalProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [formData, setFormData] = useState<FormData>({
    type: "",
    description: "",
    severity: "minor",
    contactInfo: "",
    images: [],
  });

  const [imagePreviews, setImagePreviews] = useState<string[]>([]);

  const createReportMutation = useMutation({
    mutationFn: async (data: FormData) => {
      if (!coordinates) throw new Error("No coordinates provided");

      const formDataToSend = new FormData();
      formDataToSend.append("type", data.type);
      formDataToSend.append("description", data.description);
      formDataToSend.append("severity", data.severity);
      formDataToSend.append("latitude", coordinates[1].toString());
      formDataToSend.append("longitude", coordinates[0].toString());
      formDataToSend.append("contactInfo", data.contactInfo);
      
      data.images.forEach((image) => {
        formDataToSend.append("images", image);
      });

      const response = await fetch("/api/reports", {
        method: "POST",
        body: formDataToSend,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to create report");
      }

      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Report submitted successfully!",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/reports"] });
      resetForm();
      onReportSubmitted();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to submit report",
        variant: "destructive",
      });
    },
  });

  const resetForm = () => {
    setFormData({
      type: "",
      description: "",
      severity: "minor",
      contactInfo: "",
      images: [],
    });
    setImagePreviews([]);
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    
    if (formData.images.length + files.length > 5) {
      toast({
        title: "Too many images",
        description: "Maximum 5 images allowed",
        variant: "destructive",
      });
      return;
    }

    const newImages = [...formData.images, ...files];
    setFormData(prev => ({ ...prev, images: newImages }));

    // Create previews
    files.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreviews(prev => [...prev, e.target?.result as string]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index: number) => {
    const newImages = formData.images.filter((_, i) => i !== index);
    const newPreviews = imagePreviews.filter((_, i) => i !== index);
    setFormData(prev => ({ ...prev, images: newImages }));
    setImagePreviews(newPreviews);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.type || !formData.description || formData.description.length < 10) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields with sufficient detail",
        variant: "destructive",
      });
      return;
    }

    createReportMutation.mutate(formData);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-md mx-auto max-h-[90vh] overflow-hidden animate-slide-up">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-neutral-900">Report Accident</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="text-neutral-400 hover:text-neutral-600 p-1"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Location Display */}
            <div>
              <Label className="text-sm font-medium text-neutral-700 mb-2">Location</Label>
              <div className="flex items-center space-x-2 p-3 bg-neutral-50 rounded-lg">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span className="text-sm text-neutral-600">
                  {coordinates ? 
                    `${coordinates[1].toFixed(4)}, ${coordinates[0].toFixed(4)}` : 
                    "No location selected"
                  }
                </span>
              </div>
            </div>

            {/* Accident Type */}
            <div>
              <Label htmlFor="accidentType" className="text-sm font-medium text-neutral-700 mb-2">
                Accident Type *
              </Label>
              <Select
                value={formData.type}
                onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select accident type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="vehicle-collision">Vehicle Collision</SelectItem>
                  <SelectItem value="pedestrian-accident">Pedestrian Accident</SelectItem>
                  <SelectItem value="bicycle-accident">Bicycle Accident</SelectItem>
                  <SelectItem value="road-hazard">Road Hazard</SelectItem>
                  <SelectItem value="traffic-signal">Traffic Signal Issue</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Description */}
            <div>
              <Label htmlFor="description" className="text-sm font-medium text-neutral-700 mb-2">
                Description *
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe what happened..."
                rows={3}
                className="resize-none"
                required
              />
              <p className="text-xs text-neutral-500 mt-1">Minimum 10 characters</p>
            </div>

            {/* Severity */}
            <div>
              <Label className="text-sm font-medium text-neutral-700 mb-2">Severity</Label>
              <RadioGroup
                value={formData.severity}
                onValueChange={(value) => setFormData(prev => ({ ...prev, severity: value }))}
                className="flex space-x-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="minor" id="minor" />
                  <Label htmlFor="minor" className="text-sm">Minor</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="major" id="major" />
                  <Label htmlFor="major" className="text-sm">Major</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="critical" id="critical" />
                  <Label htmlFor="critical" className="text-sm">Critical</Label>
                </div>
              </RadioGroup>
            </div>

            {/* Image Upload */}
            <div>
              <Label className="text-sm font-medium text-neutral-700 mb-2">Photos</Label>
              <div className="border-2 border-dashed border-neutral-300 rounded-lg p-4 text-center hover:border-primary transition-colors cursor-pointer">
                <input
                  type="file"
                  id="imageUpload"
                  multiple
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
                <label htmlFor="imageUpload" className="cursor-pointer">
                  <Upload className="w-8 h-8 text-neutral-400 mx-auto mb-2" />
                  <p className="text-sm text-neutral-600">Click to upload or drag photos here</p>
                  <p className="text-xs text-neutral-500 mt-1">Max 5 photos, 10MB each</p>
                </label>
              </div>
              
              {/* Image Previews */}
              {imagePreviews.length > 0 && (
                <div className="mt-3 image-preview-grid">
                  {imagePreviews.map((preview, index) => (
                    <div key={index} className="image-preview-item">
                      <img src={preview} alt={`Preview ${index + 1}`} />
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="image-preview-remove"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Contact Info */}
            <div>
              <Label htmlFor="contactInfo" className="text-sm font-medium text-neutral-700 mb-2">
                Contact Info (Optional)
              </Label>
              <Input
                id="contactInfo"
                value={formData.contactInfo}
                onChange={(e) => setFormData(prev => ({ ...prev, contactInfo: e.target.value }))}
                placeholder="Phone or email (optional)"
              />
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createReportMutation.isPending}
                className="flex-1"
              >
                {createReportMutation.isPending ? "Submitting..." : "Submit Report"}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
