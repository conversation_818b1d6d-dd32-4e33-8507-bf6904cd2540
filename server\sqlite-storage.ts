import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import { type User, type InsertUser, type AccidentReport, type InsertAccidentReport } from "@shared/schema";
import { randomUUID } from "crypto";
import { IStorage } from "./storage";

export class SQLiteStorage implements IStorage {
  private db: Database.Database;

  constructor() {
    // Create data directory if it doesn't exist
    const dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    // Initialize SQLite database
    const dbPath = path.join(dataDir, 'accident_reports.db');
    this.db = new Database(dbPath);
    
    // Enable WAL mode for better performance
    this.db.pragma('journal_mode = WAL');
    
    this.initializeTables();
  }

  private initializeTables(): void {
    // Create users table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL
      )
    `);

    // Create accident_reports table with spatial columns
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS accident_reports (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        description TEXT NOT NULL,
        severity TEXT NOT NULL DEFAULT 'minor',
        latitude REAL NOT NULL,
        longitude REAL NOT NULL,
        address TEXT,
        contact_info TEXT,
        image_urls TEXT, -- JSON array as text
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create spatial index for better performance on location queries
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_accident_reports_location 
      ON accident_reports (latitude, longitude)
    `);

    // Create index on created_at for recent reports queries
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_accident_reports_created_at 
      ON accident_reports (created_at)
    `);
  }

  // User methods
  async getUser(id: string): Promise<User | undefined> {
    const stmt = this.db.prepare('SELECT * FROM users WHERE id = ?');
    const row = stmt.get(id) as any;
    return row || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const stmt = this.db.prepare('SELECT * FROM users WHERE username = ?');
    const row = stmt.get(username) as any;
    return row || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = randomUUID();
    const user: User = { ...insertUser, id };
    
    const stmt = this.db.prepare(`
      INSERT INTO users (id, username, password) 
      VALUES (?, ?, ?)
    `);
    
    stmt.run(user.id, user.username, user.password);
    return user;
  }

  // Accident report methods
  async getAccidentReport(id: string): Promise<AccidentReport | undefined> {
    const stmt = this.db.prepare('SELECT * FROM accident_reports WHERE id = ?');
    const row = stmt.get(id) as any;
    
    if (!row) return undefined;
    
    return this.mapRowToReport(row);
  }

  async getAllAccidentReports(): Promise<AccidentReport[]> {
    const stmt = this.db.prepare(`
      SELECT * FROM accident_reports 
      ORDER BY created_at DESC
    `);
    
    const rows = stmt.all() as any[];
    return rows.map(row => this.mapRowToReport(row));
  }

  async getRecentAccidentReports(daysBack: number = 7): Promise<AccidentReport[]> {
    const stmt = this.db.prepare(`
      SELECT * FROM accident_reports 
      WHERE datetime(created_at) >= datetime('now', '-' || ? || ' days')
      ORDER BY created_at DESC
    `);
    
    const rows = stmt.all(daysBack) as any[];
    return rows.map(row => this.mapRowToReport(row));
  }

  async createAccidentReport(insertReport: InsertAccidentReport): Promise<AccidentReport> {
    const id = randomUUID();
    const now = new Date();
    
    const stmt = this.db.prepare(`
      INSERT INTO accident_reports (
        id, type, description, severity, latitude, longitude, 
        address, contact_info, image_urls, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const imageUrlsJson = JSON.stringify(insertReport.imageUrls || []);
    
    stmt.run(
      id,
      insertReport.type,
      insertReport.description,
      insertReport.severity || 'minor',
      insertReport.latitude,
      insertReport.longitude,
      insertReport.address || null,
      insertReport.contactInfo || null,
      imageUrlsJson,
      now.toISOString()
    );

    const report: AccidentReport = {
      ...insertReport,
      id,
      address: insertReport.address || null,
      contactInfo: insertReport.contactInfo || null,
      imageUrls: insertReport.imageUrls || [],
      createdAt: now,
    };

    return report;
  }

  async updateAccidentReport(id: string, updateData: Partial<AccidentReport>): Promise<AccidentReport | undefined> {
    const existing = await this.getAccidentReport(id);
    if (!existing) return undefined;

    const fields: string[] = [];
    const values: any[] = [];

    Object.entries(updateData).forEach(([key, value]) => {
      if (key !== 'id' && key !== 'createdAt') {
        if (key === 'imageUrls') {
          fields.push('image_urls = ?');
          values.push(JSON.stringify(value));
        } else if (key === 'contactInfo') {
          fields.push('contact_info = ?');
          values.push(value);
        } else {
          fields.push(`${key} = ?`);
          values.push(value);
        }
      }
    });

    if (fields.length === 0) return existing;

    values.push(id);
    const stmt = this.db.prepare(`
      UPDATE accident_reports 
      SET ${fields.join(', ')} 
      WHERE id = ?
    `);
    
    stmt.run(...values);
    return await this.getAccidentReport(id);
  }

  async deleteAccidentReport(id: string): Promise<boolean> {
    const stmt = this.db.prepare('DELETE FROM accident_reports WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  // Helper method to map database row to AccidentReport
  private mapRowToReport(row: any): AccidentReport {
    return {
      id: row.id,
      type: row.type,
      description: row.description,
      severity: row.severity,
      latitude: row.latitude,
      longitude: row.longitude,
      address: row.address,
      contactInfo: row.contact_info,
      imageUrls: row.image_urls ? JSON.parse(row.image_urls) : [],
      createdAt: new Date(row.created_at),
    };
  }

  // Spatial query methods (bonus features for GIS)
  async getReportsWithinRadius(centerLat: number, centerLon: number, radiusKm: number): Promise<AccidentReport[]> {
    // Simple distance calculation using Haversine formula approximation
    const stmt = this.db.prepare(`
      SELECT *, 
        (6371 * acos(
          cos(radians(?)) * cos(radians(latitude)) * 
          cos(radians(longitude) - radians(?)) + 
          sin(radians(?)) * sin(radians(latitude))
        )) AS distance
      FROM accident_reports
      WHERE (6371 * acos(
        cos(radians(?)) * cos(radians(latitude)) * 
        cos(radians(longitude) - radians(?)) + 
        sin(radians(?)) * sin(radians(latitude))
      )) <= ?
      ORDER BY distance
    `);
    
    const rows = stmt.all(
      centerLat, centerLon, centerLat,
      centerLat, centerLon, centerLat, radiusKm
    ) as any[];
    
    return rows.map(row => this.mapRowToReport(row));
  }

  // Clean up method
  close(): void {
    this.db.close();
  }
}