# MapDataPoint - Accident Reporter GIS Application

A full-stack web application for reporting and visualizing accident data with interactive maps and real-time data management.

## 🏗️ Architecture Overview

### **Frontend (React + TypeScript)**
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite 5.4.19
- **UI Library**: Radix UI + shadcn/ui components
- **Styling**: TailwindCSS 3.4.17
- **Maps**: OpenLayers (ol) for GIS functionality
- **State Management**: TanStack Query for server state
- **Routing**: Wouter for client-side routing

### **Backend (Node.js + Express)**
- **Runtime**: Node.js 18+ 
- **Framework**: Express.js with TypeScript
- **Database**: SQLite with better-sqlite3
- **ORM**: Drizzle ORM (configured but using raw SQL)
- **File Uploads**: Multer for image handling
- **Authentication**: Passport.js with local strategy
- **Session Management**: Express-session with memory store

### **Database Schema**
```sql
-- Users table
users (
  id TEXT PRIMARY KEY,
  username TEXT UNIQUE NOT NULL,
  password TEXT NOT NULL
)

-- Accident reports with spatial data
accident_reports (
  id TEXT PRIMARY KEY,
  type TEXT NOT NULL,
  description TEXT NOT NULL,
  severity TEXT DEFAULT 'minor',
  latitude REAL NOT NULL,
  longitude REAL NOT NULL,
  address TEXT,
  contact_info TEXT,
  image_urls TEXT, -- JSON array as text
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
```

## 📁 Project Structure

```
MapDataPoint/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   └── main.tsx        # App entry point
│   └── index.html          # HTML template
├── server/                 # Express backend
│   ├── index.ts            # Server entry point
│   ├── routes.ts           # API route definitions
│   ├── sqlite-storage.ts   # Database operations
│   ├── storage.ts          # Storage interface
│   └── vite.ts             # Vite dev server integration
├── shared/                 # Shared TypeScript schemas
│   └── schema.ts           # Zod schemas for validation
├── data/                   # SQLite database files
│   ├── accident_reports.db # Main database
│   ├── accident_reports.db-shm # Shared memory file
│   └── accident_reports.db-wal # Write-ahead log
├── uploads/                # User uploaded images
├── dist/                   # Production build output
│   ├── index.js            # Compiled server
│   └── public/             # Static frontend assets
│       ├── index.html
│       └── assets/         # JS/CSS bundles
├── package.json            # Dependencies and scripts
├── vite.config.ts          # Vite configuration
├── tailwind.config.ts      # TailwindCSS configuration
└── tsconfig.json           # TypeScript configuration
```

## 🚀 Quick Start

### Prerequisites
- **Node.js**: Version 18.16.0 or higher
- **npm**: Version 9.5.1 or higher
- **Storage**: Minimum 1GB free space

### Installation & Setup

1. **Clone/Download** the project to your desired directory
2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Build the application**:
   ```bash
   npm run build
   ```

4. **Start development server**:
   ```bash
   npm run dev
   ```
   Or for production:
   ```bash
   npm start
   ```

### Available Scripts

```json
{
  "dev": "NODE_ENV=development tsx server/index.ts",
  "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist",
  "start": "NODE_ENV=production node dist/index.js",
  "check": "tsc"
}
```

## 🌐 API Endpoints

### **Authentication**
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `POST /api/auth/register` - User registration
- `GET /api/auth/user` - Get current user

### **Accident Reports**
- `GET /api/reports` - Get all accident reports
- `GET /api/reports/recent` - Get recent reports (7 days)
- `GET /api/reports/:id` - Get specific report
- `POST /api/reports` - Create new report
- `PUT /api/reports/:id` - Update report
- `DELETE /api/reports/:id` - Delete report

### **File Uploads**
- `POST /api/upload` - Upload images
- `GET /uploads/:filename` - Serve uploaded files

### **Spatial Queries**
- `GET /api/reports/nearby?lat=X&lon=Y&radius=Z` - Get reports within radius

## 🗄️ Database Operations

The application uses **SQLiteStorage** class for all database operations:

```typescript
// Key methods available:
- getUser(id: string)
- getUserByUsername(username: string)
- createUser(insertUser: InsertUser)
- getAllAccidentReports()
- getRecentAccidentReports(daysBack: number)
- createAccidentReport(insertReport: InsertAccidentReport)
- updateAccidentReport(id: string, updateData: Partial<AccidentReport>)
- deleteAccidentReport(id: string)
- getReportsWithinRadius(centerLat: number, centerLon: number, radiusKm: number)
```

## 🔧 Configuration

### **Environment Variables**
```bash
NODE_ENV=development|production
PORT=5000                    # Server port
UPLOAD_DIR=./uploads         # File upload directory
DATABASE_PATH=./data/accident_reports.db
```

### **Important Configuration Files**
- `vite.config.ts` - Frontend build configuration
- `tailwind.config.ts` - Styling configuration  
- `tsconfig.json` - TypeScript compiler options
- `package.json` - Dependencies and scripts

## 🚀 Deployment Options

### **Option A: Node.js Only (Current Setup)**
- Single server handles both frontend and backend
- Run on port 5000: `http://localhost:5000`
- Simplest deployment option

### **Option B: IIS + Node.js Hybrid**
- IIS serves static frontend from `dist/public/`
- Node.js backend runs on separate port
- Requires URL rewrite rules for API proxying

### **Option C: Production Server**
- Use PM2 for process management
- Nginx reverse proxy
- SSL certificate setup
- See `deployment-guide.md` for detailed instructions

## 🛠️ Development Workflow

### **Frontend Development**
```bash
# Start dev server with hot reload
npm run dev

# Build for production
npm run build

# Type checking
npm run check
```

### **Backend Development**
- Server auto-restarts with tsx in development
- SQLite database auto-initializes on first run
- API routes defined in `server/routes.ts`
- Database operations in `server/sqlite-storage.ts`

### **Adding New Features**
1. **Database**: Update schema in `sqlite-storage.ts`
2. **API**: Add routes in `server/routes.ts`
3. **Types**: Update schemas in `shared/schema.ts`
4. **Frontend**: Add components in `client/src/`

## 🔍 Troubleshooting

### **Common Issues**

1. **"import.meta.dirname" errors**
   - Fixed in Node.js 18 compatibility updates
   - Uses `fileURLToPath(import.meta.url)` instead

2. **Database locked errors**
   - Ensure only one server instance running
   - Check for zombie processes

3. **Port already in use**
   - Change PORT environment variable
   - Kill existing Node.js processes

4. **Build failures**
   - Clear `node_modules` and reinstall
   - Check Node.js version compatibility

### **Logs and Debugging**
- Server logs: Console output shows API requests
- Database: Located in `data/` directory
- Uploads: Stored in `uploads/` directory
- Build output: Generated in `dist/` directory

## 📦 Key Dependencies

### **Production Dependencies**
- `express` - Web framework
- `better-sqlite3` - SQLite database
- `react` + `react-dom` - Frontend framework
- `ol` - OpenLayers mapping
- `@radix-ui/*` - UI components
- `tailwindcss` - CSS framework

### **Development Dependencies**
- `vite` - Build tool
- `tsx` - TypeScript execution
- `typescript` - Type checking
- `@vitejs/plugin-react` - React support

## 🔐 Security Considerations

- **File Uploads**: Limited to images, size restrictions
- **Database**: SQLite file permissions
- **Sessions**: Memory store (consider Redis for production)
- **Input Validation**: Zod schemas for API validation
- **CORS**: Configured for development

## 📈 Performance Notes

- **Frontend**: Code splitting with Vite
- **Database**: Spatial indexes on lat/lon columns
- **Caching**: HTTP caching headers set
- **Bundle Size**: ~644KB JavaScript, ~62KB CSS

---

**Last Updated**: August 2025  
**Node.js Version**: 18.16.0  
**Application Status**: ✅ Fully Operational
