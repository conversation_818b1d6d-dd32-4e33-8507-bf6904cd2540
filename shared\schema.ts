import { sql } from "drizzle-orm";
import { pgTable, text, varchar, timestamp, real, integer } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const accidentReports = pgTable("accident_reports", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  type: text("type").notNull(),
  description: text("description").notNull(),
  severity: text("severity").notNull().default("minor"),
  latitude: real("latitude").notNull(),
  longitude: real("longitude").notNull(),
  address: text("address"),
  contactInfo: text("contact_info"),
  imageUrls: text("image_urls").array().default([]),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertAccidentReportSchema = createInsertSchema(accidentReports).omit({
  id: true,
  createdAt: true,
}).extend({
  description: z.string().min(10, "Description must be at least 10 characters"),
  type: z.enum(["vehicle-collision", "pedestrian-accident", "bicycle-accident", "road-hazard", "traffic-signal", "other"]),
  severity: z.enum(["minor", "major", "critical"]).default("minor"),
  latitude: z.number().min(-90).max(90),
  longitude: z.number().min(-180).max(180),
});

export type InsertAccidentReport = z.infer<typeof insertAccidentReportSchema>;
export type AccidentReport = typeof accidentReports.$inferSelect;

export const users = pgTable("users", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
