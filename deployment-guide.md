# Deployment Guide - Accident Reporter App

## Overview
This guide explains how to deploy the Accident Reporter GIS application to your own web hosting service.

## System Requirements

### Server Requirements
- **Node.js**: Version 18 or higher
- **Storage**: At least 1GB free space for database and uploaded images
- **RAM**: Minimum 512MB (1GB recommended)
- **Operating System**: Linux, Windows, or macOS

### Hosting Options
- **VPS/Cloud**: DigitalOcean, AWS EC2, Linode, Vultr
- **Shared Hosting**: Must support Node.js applications
- **Self-hosted**: Any server with Node.js capability

## Pre-deployment Setup

### 1. Install Node.js on Your Server
```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# CentOS/RHEL
curl -fsSL https://rpm.nodesource.com/setup_20.x | sudo bash -
sudo yum install -y nodejs

# Verify installation
node --version
npm --version
```

### 2. Create Application Directory
```bash
mkdir accident-reporter
cd accident-reporter
```

## Deployment Steps

### 1. Upload Application Files
Upload all project files to your server, maintaining the directory structure:

```
accident-reporter/
├── client/          # Frontend React application
├── server/          # Backend Express server
├── shared/          # Shared TypeScript schemas
├── data/           # SQLite database (will be created)
├── uploads/        # Image storage (will be created)
├── package.json    # Dependencies
├── package-lock.json
├── vite.config.ts  # Build configuration
├── tsconfig.json   # TypeScript configuration
└── tailwind.config.ts
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Build the Application
```bash
npm run build
```

### 4. Configure Environment Variables
Create a `.env` file in the root directory:

```env
NODE_ENV=production
PORT=5000
UPLOAD_DIR=./uploads
DATABASE_PATH=./data/accident_reports.db
```

### 5. Set Up Process Manager (PM2)
Install PM2 for production process management:

```bash
# Install PM2 globally
npm install -g pm2

# Create PM2 ecosystem file
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'accident-reporter',
    script: 'npm',
    args: 'run start',
    cwd: '/path/to/your/accident-reporter',
    env: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF

# Create logs directory
mkdir -p logs

# Start the application
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 6. Configure Reverse Proxy (Nginx)
Create Nginx configuration:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # Serve static files
    location /uploads/ {
        alias /path/to/your/accident-reporter/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Proxy API requests
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Serve frontend application
    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 7. Set Up SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt-get install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal (add to crontab)
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Production Configuration

### 1. Update package.json Scripts
Add production scripts to package.json:

```json
{
  "scripts": {
    "start": "NODE_ENV=production tsx server/index.ts",
    "build": "vite build",
    "preview": "vite preview"
  }
}
```

### 2. Database Backup Strategy
Create automatic backup script:

```bash
#!/bin/bash
# backup-db.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/path/to/backups"
DB_PATH="/path/to/accident-reporter/data/accident_reports.db"

mkdir -p $BACKUP_DIR
cp $DB_PATH "$BACKUP_DIR/accident_reports_$DATE.db"

# Keep only last 30 days of backups
find $BACKUP_DIR -name "accident_reports_*.db" -mtime +30 -delete
```

Add to crontab for daily backups:
```bash
0 2 * * * /path/to/backup-db.sh
```

### 3. Log Rotation
Configure log rotation in `/etc/logrotate.d/accident-reporter`:

```
/path/to/accident-reporter/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
```

## Security Considerations

### 1. File Upload Security
- Limit file types to images only
- Set maximum file size limits
- Scan uploaded files for malware
- Store uploads outside web root when possible

### 2. Database Security
- Regular backups to secure location
- Restrict file permissions on database file
- Consider encryption for sensitive data

### 3. Server Security
- Keep Node.js and dependencies updated
- Use firewall to restrict access
- Regular security updates
- Monitor application logs

## Monitoring and Maintenance

### 1. Application Monitoring
```bash
# Check application status
pm2 status
pm2 logs accident-reporter

# Restart application
pm2 restart accident-reporter

# Monitor resources
pm2 monit
```

### 2. Database Maintenance
```bash
# Check database size
du -h data/accident_reports.db

# Optimize database (run monthly)
sqlite3 data/accident_reports.db "VACUUM;"
```

### 3. Update Process
```bash
# Stop application
pm2 stop accident-reporter

# Update code
git pull  # or upload new files

# Install dependencies
npm install

# Build application
npm run build

# Start application
pm2 start accident-reporter
```

## Troubleshooting

### Common Issues
1. **Port already in use**: Change PORT in .env file
2. **Permission denied**: Check file permissions and ownership
3. **Database locked**: Ensure only one process accesses database
4. **Upload failures**: Check uploads directory permissions

### Support
- Check application logs: `pm2 logs accident-reporter`
- Monitor system resources: `htop` or `pm2 monit`
- Verify database integrity: `sqlite3 data/accident_reports.db ".integrity_check"`

## Performance Optimization

### 1. Enable Gzip Compression
Add to Nginx configuration:
```nginx
gzip on;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
```

### 2. Database Optimization
```sql
-- Run monthly for better performance
PRAGMA optimize;
VACUUM;
ANALYZE;
```

### 3. Image Optimization
Consider adding image compression middleware for uploaded photos.